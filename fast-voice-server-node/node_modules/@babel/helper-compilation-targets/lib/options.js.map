{"version": 3, "names": ["TargetNames", "exports", "node", "deno", "chrome", "opera", "edge", "firefox", "safari", "ie", "ios", "android", "electron", "samsung", "rhino", "opera_mobile"], "sources": ["../src/options.ts"], "sourcesContent": ["export const TargetNames = {\n  node: \"node\",\n  deno: \"deno\",\n  chrome: \"chrome\",\n  opera: \"opera\",\n  edge: \"edge\",\n  firefox: \"firefox\",\n  safari: \"safari\",\n  ie: \"ie\",\n  ios: \"ios\",\n  android: \"android\",\n  electron: \"electron\",\n  samsung: \"samsung\",\n  rhino: \"rhino\",\n  opera_mobile: \"opera_mobile\",\n};\n"], "mappings": ";;;;;;AAAO,MAAMA,WAAW,GAAAC,OAAA,CAAAD,WAAA,GAAG;EACzBE,IAAI,EAAE,MAAM;EACZC,IAAI,EAAE,MAAM;EACZC,MAAM,EAAE,QAAQ;EAChBC,KAAK,EAAE,OAAO;EACdC,IAAI,EAAE,MAAM;EACZC,OAAO,EAAE,SAAS;EAClBC,MAAM,EAAE,QAAQ;EAChBC,EAAE,EAAE,IAAI;EACRC,GAAG,EAAE,KAAK;EACVC,OAAO,EAAE,SAAS;EAClBC,QAAQ,EAAE,UAAU;EACpBC,OAAO,EAAE,SAAS;EAClBC,KAAK,EAAE,OAAO;EACdC,YAAY,EAAE;AAChB,CAAC", "ignoreList": []}