{"version": 3, "names": ["_interopRequireWildcard", "obj", "nodeInterop", "WeakMap", "cacheBabelInterop", "cacheNodeInterop", "exports", "default", "__esModule", "_", "newObj", "__proto__", "desc", "has", "get", "set", "key", "hasOwnProperty", "call", "Object", "defineProperty", "getOwnPropertyDescriptor"], "sources": ["../../src/helpers/interopRequireWildcard.ts"], "sourcesContent": ["/* @minVersion 7.14.0 */\n\nexport default function _interopRequireWildcard(\n  obj: any,\n  nodeInterop: boolean,\n) {\n  if (typeof WeakMap === \"function\") {\n    var cacheBabelInterop = new WeakMap();\n    var cacheNodeInterop = new WeakMap();\n  }\n\n  // @ts-expect-error: assign to function\n  return (_interopRequireWildcard = function (obj: any, nodeInterop: boolean) {\n    if (!nodeInterop && obj && obj.__esModule) {\n      return obj;\n    }\n    // Temporary variable for output size\n    var _;\n    var newObj: { [key: string]: any } = { __proto__: null, default: obj };\n    var desc: PropertyDescriptor | undefined;\n\n    if (\n      obj === null ||\n      (typeof obj !== \"object\" && typeof obj !== \"function\")\n    ) {\n      return newObj;\n    }\n\n    _ = nodeInterop ? cacheNodeInterop : cacheBabelInterop;\n    if (_) {\n      if (_.has(obj)) return _.get(obj);\n      _.set(obj, newObj);\n    }\n\n    for (const key in obj) {\n      if (key !== \"default\" && {}.hasOwnProperty.call(obj, key)) {\n        desc =\n          (_ = Object.defineProperty) &&\n          Object.getOwnPropertyDescriptor(obj, key);\n        if (desc && (desc.get || desc.set)) {\n          _(newObj, key, desc);\n        } else {\n          newObj[key] = obj[key];\n        }\n      }\n    }\n    return newObj;\n  })(obj, nodeInterop);\n}\n"], "mappings": ";;;;;;AAEe,SAASA,uBAAuBA,CAC7CC,GAAQ,EACRC,WAAoB,EACpB;EACA,IAAI,OAAOC,OAAO,KAAK,UAAU,EAAE;IACjC,IAAIC,iBAAiB,GAAG,IAAID,OAAO,CAAC,CAAC;IACrC,IAAIE,gBAAgB,GAAG,IAAIF,OAAO,CAAC,CAAC;EACtC;EAGA,OAAO,CAAAG,OAAA,CAAAC,OAAA,GAACP,uBAAuB,GAAG,SAAAA,CAAUC,GAAQ,EAAEC,WAAoB,EAAE;IAC1E,IAAI,CAACA,WAAW,IAAID,GAAG,IAAIA,GAAG,CAACO,UAAU,EAAE;MACzC,OAAOP,GAAG;IACZ;IAEA,IAAIQ,CAAC;IACL,IAAIC,MAA8B,GAAG;MAAEC,SAAS,EAAE,IAAI;MAAEJ,OAAO,EAAEN;IAAI,CAAC;IACtE,IAAIW,IAAoC;IAExC,IACEX,GAAG,KAAK,IAAI,IACX,OAAOA,GAAG,KAAK,QAAQ,IAAI,OAAOA,GAAG,KAAK,UAAW,EACtD;MACA,OAAOS,MAAM;IACf;IAEAD,CAAC,GAAGP,WAAW,GAAGG,gBAAgB,GAAGD,iBAAiB;IACtD,IAAIK,CAAC,EAAE;MACL,IAAIA,CAAC,CAACI,GAAG,CAACZ,GAAG,CAAC,EAAE,OAAOQ,CAAC,CAACK,GAAG,CAACb,GAAG,CAAC;MACjCQ,CAAC,CAACM,GAAG,CAACd,GAAG,EAAES,MAAM,CAAC;IACpB;IAEA,KAAK,MAAMM,GAAG,IAAIf,GAAG,EAAE;MACrB,IAAIe,GAAG,KAAK,SAAS,IAAI,CAAC,CAAC,CAACC,cAAc,CAACC,IAAI,CAACjB,GAAG,EAAEe,GAAG,CAAC,EAAE;QACzDJ,IAAI,GACF,CAACH,CAAC,GAAGU,MAAM,CAACC,cAAc,KAC1BD,MAAM,CAACE,wBAAwB,CAACpB,GAAG,EAAEe,GAAG,CAAC;QAC3C,IAAIJ,IAAI,KAAKA,IAAI,CAACE,GAAG,IAAIF,IAAI,CAACG,GAAG,CAAC,EAAE;UAClCN,CAAC,CAACC,MAAM,EAAEM,GAAG,EAAEJ,IAAI,CAAC;QACtB,CAAC,MAAM;UACLF,MAAM,CAACM,GAAG,CAAC,GAAGf,GAAG,CAACe,GAAG,CAAC;QACxB;MACF;IACF;IACA,OAAON,MAAM;EACf,CAAC,EAAET,GAAG,EAAEC,WAAW,CAAC;AACtB", "ignoreList": []}