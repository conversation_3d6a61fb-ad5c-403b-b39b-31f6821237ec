# 🚀 Fast Voice Server - Ultra-Low Latency Voice Chat

Optimalizovaný server pre hlasový chat v Rust s fokusem na minimálnu latenciu a maximálny výkon.

## 🎯 Ciele optimalizácie

### Súčasná architektúra (Python Flask):
- **STT latencia**: 800-1500ms
- **LLM latencia**: 1000-2000ms  
- **TTS latencia**: 500-1000ms
- **Celková latencia**: 2300-4500ms

### Cieľová architektúra (Rust):
- **STT latencia**: 200-500ms
- **LLM latencia**: 300-800ms
- **TTS latencia**: 100-300ms
- **Celková latencia**: 600-1600ms

## 🔧 Kľúčové optimalizácie

### 1. **Rust Performance**
- Zero-cost abstractions
- Memory safety bez garbage collection
- Paralelné spracovanie s Tokio
- Optimalizované HTTP handling s Axum

### 2. **Pipeline Optimizations**
- Paralelné volania API
- Streaming processing
- Memory pooling
- Connection reuse

### 3. **Caching Strategy**
- TTS cache pre časté frázy
- Connection pooling
- Preloaded models
- Smart audio buffering

### 4. **Real-time Features**
- WebSocket streaming
- Partial transcript updates
- Progressive audio synthesis
- Low-latency audio processing

## 🏗️ Architektúra

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Frontend      │    │   Rust Server    │    │   External APIs │
│                 │    │                  │    │                 │
│ ┌─────────────┐ │    │ ┌──────────────┐ │    │ ┌─────────────┐ │
│ │ Microphone  │ │───▶│ │ Audio Proc.  │ │───▶│ │ Deepgram    │ │
│ └─────────────┘ │    │ └──────────────┘ │    │ │ STT         │ │
│                 │    │        │         │    │ └─────────────┘ │
│ ┌─────────────┐ │    │        ▼         │    │                 │
│ │ Speaker     │ │◀───│ ┌──────────────┐ │    │ ┌─────────────┐ │
│ └─────────────┘ │    │ │ TTS Engine   │ │◀───│ │ OpenAI      │ │
│                 │    │ └──────────────┘ │    │ │ GPT         │ │
└─────────────────┘    │        ▲         │    │ └─────────────┘ │
                       │        │         │    │                 │
                       │ ┌──────────────┐ │    │ ┌─────────────┐ │
                       │ │ WebSocket    │ │    │ │ Piper TTS   │ │
                       │ │ Handler      │ │    │ │ (Local)     │ │
                       │ └──────────────┘ │    │ └─────────────┘ │
                       └──────────────────┘    └─────────────────┘
```

## 🚀 Rýchle spustenie

### Lokálny development:
```bash
# Build a spustenie
cargo build --release
cargo run

# Test endpoints
curl http://localhost:5000/health
curl http://localhost:5000/api/metrics
```

### Deployment na Oracle Cloud:
```bash
# Automatický deployment
chmod +x deploy.sh
./deploy.sh

# Manuálny deployment
scp -i "ssh-key.key" target/release/fast-voice-server ubuntu@*************:/home/<USER>/
ssh -i "ssh-key.key" ubuntu@************* "./fast-voice-server"
```

## 📊 API Endpoints

### REST API:
- `GET /health` - Health check
- `POST /api/voice-chat` - Complete voice pipeline
- `POST /api/transcribe` - STT only
- `POST /api/chat` - LLM only  
- `POST /api/speak` - TTS only
- `GET /api/metrics` - Performance metrics

### WebSocket API:
- `WS /ws` - Real-time streaming voice chat
- Podporuje partial transcripts
- Progressive audio synthesis
- Bi-directional streaming

## 🔧 Konfigurácia

### Environment variables:
```bash
export RUST_LOG=info
export DEEPGRAM_API_KEY="your_key"
export OPENAI_API_KEY="your_key"
export PIPER_MODEL_PATH="/path/to/model.onnx"
```

### Performance tuning:
```bash
# Systémové optimalizácie
echo 'net.core.rmem_max = 134217728' >> /etc/sysctl.conf
echo 'net.core.wmem_max = 134217728' >> /etc/sysctl.conf
echo 'fs.file-max = 65536' >> /etc/sysctl.conf

# Aplikačné optimalizácie
export TOKIO_WORKER_THREADS=4
export RUST_BACKTRACE=1
```

## 📈 Benchmarking

### Load testing:
```bash
# Install wrk
sudo apt install wrk

# Test health endpoint
wrk -t4 -c100 -d30s http://localhost:5000/health

# Test voice chat endpoint
# (requires custom script for multipart data)
```

### Monitoring:
```bash
# Real-time metrics
curl http://localhost:5000/api/metrics | jq

# System monitoring
htop
iotop
nethogs
```

## 🐛 Debugging

### Logs:
```bash
# Application logs
sudo journalctl -u fast-voice-server -f

# Performance profiling
RUST_LOG=debug cargo run

# Memory profiling
valgrind --tool=massif target/release/fast-voice-server
```

### Common issues:
1. **High latency**: Check network, API keys, model paths
2. **Memory leaks**: Monitor with `htop`, check cache sizes
3. **Audio issues**: Verify Piper installation, model files
4. **Connection errors**: Check firewall, CORS settings

## 🔮 Budúce optimalizácie

### Phase 2:
- [ ] ONNX runtime integration pre lokálny STT
- [ ] GPU acceleration pre TTS
- [ ] Edge computing deployment
- [ ] Custom audio codecs

### Phase 3:
- [ ] Real-time voice cloning
- [ ] Multi-language support
- [ ] Voice activity detection
- [ ] Noise cancellation

## 📝 Licencia

MIT License - optimalizované pre maximálny výkon! 🚀
