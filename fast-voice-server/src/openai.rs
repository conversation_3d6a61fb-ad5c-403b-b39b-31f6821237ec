use anyhow::Result;
use reqwest::Client;
use serde::{Deserialize, Serialize};
use std::time::Instant;
use tracing::{info, error};
use crate::Message;

#[derive(Clone)]
pub struct OpenAIClient {
    client: Client,
    api_key: String,
    base_url: String,
}

#[derive(Debug, Serialize)]
struct ChatCompletionRequest {
    model: String,
    messages: Vec<ChatMessage>,
    max_tokens: u32,
    temperature: f32,
    stream: bool,
}

#[derive(Debug, Serialize, Deserialize)]
struct ChatMessage {
    role: String,
    content: String,
}

#[derive(Debug, Deserialize)]
struct ChatCompletionResponse {
    choices: Vec<ChatChoice>,
    usage: Option<Usage>,
}

#[derive(Debug, Deserialize)]
struct ChatChoice {
    message: ChatMessage,
    finish_reason: Option<String>,
}

#[derive(Debug, Deserialize)]
struct Usage {
    prompt_tokens: u32,
    completion_tokens: u32,
    total_tokens: u32,
}

impl OpenAIClient {
    pub fn new(api_key: String) -> Self {
        let client = Client::builder()
            .timeout(std::time::Duration::from_secs(30))
            .build()
            .expect("Failed to create HTTP client");

        Self {
            client,
            api_key,
            base_url: "https://api.openai.com/v1/chat/completions".to_string(),
        }
    }

    pub async fn chat_completion(
        &self,
        user_message: &str,
        conversation_history: &[Message],
    ) -> Result<String> {
        let start = Instant::now();

        // Build messages array with system prompt and conversation history
        let mut messages = vec![
            ChatMessage {
                role: "system".to_string(),
                content: "Si užitočný asistent, ktorý odpovedá po slovensky. Buď stručný a priateľský. Odpovedaj v 1-2 vetách.".to_string(),
            }
        ];

        // Add conversation history (keep last 10 messages for context)
        let recent_history = if conversation_history.len() > 10 {
            &conversation_history[conversation_history.len() - 10..]
        } else {
            conversation_history
        };

        for msg in recent_history {
            messages.push(ChatMessage {
                role: msg.role.clone(),
                content: msg.content.clone(),
            });
        }

        // Add current user message
        messages.push(ChatMessage {
            role: "user".to_string(),
            content: user_message.to_string(),
        });

        let request = ChatCompletionRequest {
            model: "gpt-3.5-turbo".to_string(), // Fast model for low latency
            messages,
            max_tokens: 150, // Limit response length for speed
            temperature: 0.7,
            stream: false,
        };

        let response = self
            .client
            .post(&self.base_url)
            .header("Authorization", format!("Bearer {}", self.api_key))
            .header("Content-Type", "application/json")
            .json(&request)
            .send()
            .await?;

        if !response.status().is_success() {
            let status = response.status();
            let error_text = response.text().await.unwrap_or_default();
            return Err(anyhow::anyhow!("OpenAI API error {}: {}", status, error_text));
        }

        let chat_response: ChatCompletionResponse = response.json().await?;
        
        let ai_response = chat_response
            .choices
            .first()
            .map(|choice| choice.message.content.clone())
            .unwrap_or_else(|| "Prepáčte, nemôžem teraz odpovedať.".to_string());

        let duration = start.elapsed();
        info!("🤖 OpenAI completion: {}ms - '{}'", duration.as_millis(), ai_response);

        // Log token usage if available
        if let Some(usage) = chat_response.usage {
            info!("📊 Token usage: {} prompt + {} completion = {} total", 
                  usage.prompt_tokens, usage.completion_tokens, usage.total_tokens);
        }

        Ok(ai_response)
    }

    pub async fn chat_completion_streaming(
        &self,
        user_message: &str,
        conversation_history: &[Message],
    ) -> Result<tokio::sync::mpsc::Receiver<String>> {
        // Implementation for streaming chat completion
        // This would use OpenAI's streaming API for real-time responses
        let (tx, rx) = tokio::sync::mpsc::channel(100);
        
        let client = self.clone();
        let user_message = user_message.to_string();
        let conversation_history = conversation_history.to_vec();
        
        tokio::spawn(async move {
            // Build streaming request
            let mut messages = vec![
                ChatMessage {
                    role: "system".to_string(),
                    content: "Si užitočný asistent, ktorý odpovedá po slovensky. Buď stručný a priateľský.".to_string(),
                }
            ];

            // Add conversation history
            for msg in conversation_history.iter().rev().take(10).rev() {
                messages.push(ChatMessage {
                    role: msg.role.clone(),
                    content: msg.content.clone(),
                });
            }

            messages.push(ChatMessage {
                role: "user".to_string(),
                content: user_message,
            });

            let request = ChatCompletionRequest {
                model: "gpt-3.5-turbo".to_string(),
                messages,
                max_tokens: 150,
                temperature: 0.7,
                stream: true,
            };

            // TODO: Implement actual streaming
            // For now, just send the complete response
            match client.chat_completion(&request.messages.last().unwrap().content, &[]).await {
                Ok(response) => {
                    let _ = tx.send(response).await;
                }
                Err(e) => {
                    error!("Streaming chat completion failed: {}", e);
                }
            }
        });

        Ok(rx)
    }

    pub fn get_model_info(&self) -> &str {
        "gpt-3.5-turbo"
    }

    pub fn set_model(&mut self, model: String) {
        // Allow dynamic model switching for different use cases
        // gpt-3.5-turbo for speed, gpt-4 for quality
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_openai_client_creation() {
        let client = OpenAIClient::new("test_key".to_string());
        assert_eq!(client.api_key, "test_key");
        assert_eq!(client.get_model_info(), "gpt-3.5-turbo");
    }

    #[test]
    fn test_message_building() {
        let messages = vec![
            Message {
                role: "user".to_string(),
                content: "Hello".to_string(),
                timestamp: 123456789,
            }
        ];
        
        assert_eq!(messages.len(), 1);
        assert_eq!(messages[0].role, "user");
    }
}
