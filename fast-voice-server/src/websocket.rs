use axum::{
    extract::ws::{Message, WebSocket},
    Error,
};
use futures::{sink::SinkExt, stream::StreamExt};
use serde::{Deserialize, Serialize};
use std::time::Instant;
use tracing::{info, warn, error};
use uuid::Uuid;

#[derive(Debug, Serialize, Deserialize)]
#[serde(tag = "type")]
pub enum WSMessage {
    #[serde(rename = "audio_chunk")]
    AudioChunk {
        session_id: String,
        chunk_id: u32,
        audio_data: Vec<u8>,
        is_final: bool,
    },
    #[serde(rename = "transcript_partial")]
    TranscriptPartial {
        session_id: String,
        text: String,
        confidence: f64,
    },
    #[serde(rename = "transcript_final")]
    TranscriptFinal {
        session_id: String,
        text: String,
        confidence: f64,
    },
    #[serde(rename = "ai_response")]
    AIResponse {
        session_id: String,
        text: String,
        audio_url: Option<String>,
    },
    #[serde(rename = "error")]
    Error {
        session_id: String,
        message: String,
        code: String,
    },
    #[serde(rename = "ping")]
    Ping {
        timestamp: u64,
    },
    #[serde(rename = "pong")]
    Pong {
        timestamp: u64,
    },
}

pub async fn handle_socket(socket: WebSocket) {
    let session_id = Uuid::new_v4().to_string();
    info!("🔌 New WebSocket connection: {}", session_id);
    
    let (mut sender, mut receiver) = socket.split();
    
    // Send welcome message
    let welcome = WSMessage::AIResponse {
        session_id: session_id.clone(),
        text: "Pripojenie úspešné. Môžete začať hovoriť.".to_string(),
        audio_url: None,
    };
    
    if let Ok(msg) = serde_json::to_string(&welcome) {
        if let Err(e) = sender.send(Message::Text(msg)).await {
            error!("Failed to send welcome message: {}", e);
            return;
        }
    }

    // Handle incoming messages
    while let Some(msg) = receiver.next().await {
        match msg {
            Ok(Message::Text(text)) => {
                if let Err(e) = handle_text_message(&mut sender, &session_id, text).await {
                    error!("Error handling text message: {}", e);
                    break;
                }
            }
            Ok(Message::Binary(data)) => {
                if let Err(e) = handle_binary_message(&mut sender, &session_id, data).await {
                    error!("Error handling binary message: {}", e);
                    break;
                }
            }
            Ok(Message::Close(_)) => {
                info!("🔌 WebSocket connection closed: {}", session_id);
                break;
            }
            Ok(Message::Ping(data)) => {
                if let Err(e) = sender.send(Message::Pong(data)).await {
                    error!("Failed to send pong: {}", e);
                    break;
                }
            }
            Ok(Message::Pong(_)) => {
                // Handle pong if needed
            }
            Err(e) => {
                error!("WebSocket error: {}", e);
                break;
            }
        }
    }
    
    info!("🔌 WebSocket handler finished: {}", session_id);
}

async fn handle_text_message(
    sender: &mut futures::stream::SplitSink<WebSocket, Message>,
    session_id: &str,
    text: String,
) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
    let start = Instant::now();
    
    // Parse incoming message
    let ws_message: WSMessage = serde_json::from_str(&text)?;
    
    match ws_message {
        WSMessage::Ping { timestamp } => {
            let pong = WSMessage::Pong { timestamp };
            let response = serde_json::to_string(&pong)?;
            sender.send(Message::Text(response)).await?;
        }
        WSMessage::AudioChunk { chunk_id, audio_data, is_final, .. } => {
            info!("📦 Received audio chunk {}: {} bytes (final: {})", 
                  chunk_id, audio_data.len(), is_final);
            
            // TODO: Process audio chunk
            // For now, just acknowledge receipt
            if is_final {
                let response = WSMessage::TranscriptFinal {
                    session_id: session_id.to_string(),
                    text: "Spracovávam vašu reč...".to_string(),
                    confidence: 0.9,
                };
                let response_text = serde_json::to_string(&response)?;
                sender.send(Message::Text(response_text)).await?;
            }
        }
        _ => {
            warn!("Unhandled WebSocket message type");
        }
    }
    
    let duration = start.elapsed();
    info!("⚡ WebSocket message processed in {}ms", duration.as_millis());
    
    Ok(())
}

async fn handle_binary_message(
    sender: &mut futures::stream::SplitSink<WebSocket, Message>,
    session_id: &str,
    data: Vec<u8>,
) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
    let start = Instant::now();
    
    info!("📦 Received binary audio data: {} bytes", data.len());
    
    // Process binary audio data directly
    // This is more efficient than JSON encoding for audio
    
    // TODO: Implement real-time audio processing pipeline
    // 1. Validate audio format
    // 2. Send to STT (streaming)
    // 3. Process partial transcripts
    // 4. Send to LLM when complete
    // 5. Generate TTS response
    // 6. Stream audio back
    
    // For now, send a placeholder response
    let response = WSMessage::TranscriptPartial {
        session_id: session_id.to_string(),
        text: "Počúvam...".to_string(),
        confidence: 0.5,
    };
    
    let response_text = serde_json::to_string(&response)?;
    sender.send(Message::Text(response_text)).await?;
    
    let duration = start.elapsed();
    info!("⚡ Binary message processed in {}ms", duration.as_millis());
    
    Ok(())
}

pub struct StreamingSession {
    pub session_id: String,
    pub start_time: Instant,
    pub audio_buffer: Vec<u8>,
    pub partial_transcript: String,
    pub conversation_history: Vec<crate::Message>,
}

impl StreamingSession {
    pub fn new() -> Self {
        Self {
            session_id: Uuid::new_v4().to_string(),
            start_time: Instant::now(),
            audio_buffer: Vec::new(),
            partial_transcript: String::new(),
            conversation_history: Vec::new(),
        }
    }
    
    pub fn add_audio_chunk(&mut self, chunk: Vec<u8>) {
        self.audio_buffer.extend(chunk);
    }
    
    pub fn clear_audio_buffer(&mut self) {
        self.audio_buffer.clear();
    }
    
    pub fn update_partial_transcript(&mut self, text: String) {
        self.partial_transcript = text;
    }
    
    pub fn finalize_transcript(&mut self) -> String {
        let final_transcript = self.partial_transcript.clone();
        self.partial_transcript.clear();
        final_transcript
    }
    
    pub fn add_to_history(&mut self, role: String, content: String) {
        let timestamp = std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .unwrap()
            .as_secs();
            
        self.conversation_history.push(crate::Message {
            role,
            content,
            timestamp,
        });
        
        // Keep only recent history
        if self.conversation_history.len() > 20 {
            self.conversation_history.drain(0..self.conversation_history.len() - 20);
        }
    }
    
    pub fn get_session_duration(&self) -> std::time::Duration {
        self.start_time.elapsed()
    }
}

impl Default for StreamingSession {
    fn default() -> Self {
        Self::new()
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_streaming_session_creation() {
        let session = StreamingSession::new();
        assert!(!session.session_id.is_empty());
        assert!(session.audio_buffer.is_empty());
        assert!(session.partial_transcript.is_empty());
        assert!(session.conversation_history.is_empty());
    }

    #[test]
    fn test_audio_buffer_operations() {
        let mut session = StreamingSession::new();
        
        session.add_audio_chunk(vec![1, 2, 3]);
        session.add_audio_chunk(vec![4, 5, 6]);
        
        assert_eq!(session.audio_buffer, vec![1, 2, 3, 4, 5, 6]);
        
        session.clear_audio_buffer();
        assert!(session.audio_buffer.is_empty());
    }

    #[test]
    fn test_transcript_operations() {
        let mut session = StreamingSession::new();
        
        session.update_partial_transcript("Hello".to_string());
        assert_eq!(session.partial_transcript, "Hello");
        
        let final_transcript = session.finalize_transcript();
        assert_eq!(final_transcript, "Hello");
        assert!(session.partial_transcript.is_empty());
    }

    #[test]
    fn test_conversation_history() {
        let mut session = StreamingSession::new();
        
        session.add_to_history("user".to_string(), "Hello".to_string());
        session.add_to_history("assistant".to_string(), "Hi there!".to_string());
        
        assert_eq!(session.conversation_history.len(), 2);
        assert_eq!(session.conversation_history[0].role, "user");
        assert_eq!(session.conversation_history[1].role, "assistant");
    }

    #[test]
    fn test_ws_message_serialization() {
        let msg = WSMessage::Ping { timestamp: 123456789 };
        let serialized = serde_json::to_string(&msg).unwrap();
        assert!(serialized.contains("ping"));
        assert!(serialized.contains("123456789"));
        
        let deserialized: WSMessage = serde_json::from_str(&serialized).unwrap();
        match deserialized {
            WSMessage::Ping { timestamp } => assert_eq!(timestamp, 123456789),
            _ => panic!("Wrong message type"),
        }
    }
}
