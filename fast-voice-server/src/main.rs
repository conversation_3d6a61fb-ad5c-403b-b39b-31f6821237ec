use axum::{
    extract::{Multipart, State, WebSocketUpgrade},
    http::StatusCode,
    response::{IntoResponse, Json, Response},
    routing::{get, post},
    Router,
};
use serde::{Deserialize, Serialize};
use std::{
    collections::HashMap,
    sync::Arc,
    time::{Duration, Instant},
};
use tokio::sync::{mpsc, RwLock};
use tower_http::cors::CorsLayer;
use tracing::{info, warn, error};

mod audio;
mod deepgram;
mod openai;
mod tts;
mod websocket;

use audio::AudioProcessor;
use deepgram::DeepgramClient;
use openai::OpenAIClient;
use tts::TTSEngine;

#[derive(Clone)]
pub struct AppState {
    pub deepgram: DeepgramClient,
    pub openai: OpenAIClient,
    pub tts: TTSEngine,
    pub audio_processor: AudioProcessor,
    pub conversations: Arc<RwLock<HashMap<String, Vec<Message>>>>,
    pub metrics: Arc<RwLock<Metrics>>,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct Message {
    pub role: String,
    pub content: String,
    pub timestamp: u64,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct Metrics {
    pub total_requests: u64,
    pub avg_stt_latency: f64,
    pub avg_llm_latency: f64,
    pub avg_tts_latency: f64,
    pub avg_total_latency: f64,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct VoiceChatRequest {
    pub audio_data: Vec<u8>,
    pub session_id: Option<String>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct VoiceChatResponse {
    pub transcript: String,
    pub response: String,
    pub audio_url: String,
    pub session_id: String,
    pub latency: LatencyMetrics,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct LatencyMetrics {
    pub stt_ms: u64,
    pub llm_ms: u64,
    pub tts_ms: u64,
    pub total_ms: u64,
}

#[tokio::main]
async fn main() -> anyhow::Result<()> {
    tracing_subscriber::init();
    
    info!("🚀 Starting Fast Voice Server in Rust");
    
    // Initialize components
    let deepgram = DeepgramClient::new("****************************************".to_string());
    let openai = OpenAIClient::new("***************************************************".to_string());
    let tts = TTSEngine::new().await?;
    let audio_processor = AudioProcessor::new();
    
    let state = AppState {
        deepgram,
        openai,
        tts,
        audio_processor,
        conversations: Arc::new(RwLock::new(HashMap::new())),
        metrics: Arc::new(RwLock::new(Metrics {
            total_requests: 0,
            avg_stt_latency: 0.0,
            avg_llm_latency: 0.0,
            avg_tts_latency: 0.0,
            avg_total_latency: 0.0,
        })),
    };

    let app = Router::new()
        .route("/health", get(health_check))
        .route("/api/voice-chat", post(voice_chat_handler))
        .route("/api/voice-chat-stream", post(voice_chat_stream_handler))
        .route("/api/transcribe", post(transcribe_handler))
        .route("/api/chat", post(chat_handler))
        .route("/api/speak", post(speak_handler))
        .route("/api/metrics", get(metrics_handler))
        .route("/ws", get(websocket_handler))
        .layer(CorsLayer::permissive())
        .with_state(state);

    let listener = tokio::net::TcpListener::bind("0.0.0.0:5000").await?;
    info!("🎯 Server listening on http://0.0.0.0:5000");
    
    axum::serve(listener, app).await?;
    Ok(())
}

async fn health_check() -> impl IntoResponse {
    "OK - Fast Voice Chat Server with Rust is running!"
}

async fn voice_chat_handler(
    State(state): State<AppState>,
    mut multipart: Multipart,
) -> Result<Json<VoiceChatResponse>, StatusCode> {
    let start_time = Instant::now();
    
    // Extract audio data from multipart
    let mut audio_data = Vec::new();
    let mut session_id = uuid::Uuid::new_v4().to_string();
    
    while let Some(field) = multipart.next_field().await.map_err(|_| StatusCode::BAD_REQUEST)? {
        match field.name() {
            Some("audio") => {
                audio_data = field.bytes().await.map_err(|_| StatusCode::BAD_REQUEST)?.to_vec();
            }
            Some("session_id") => {
                if let Ok(id) = field.text().await {
                    session_id = id;
                }
            }
            _ => {}
        }
    }
    
    if audio_data.is_empty() {
        return Err(StatusCode::BAD_REQUEST);
    }

    // Process audio in parallel pipeline
    let (transcript, llm_response, audio_url, latency) = 
        process_voice_pipeline(&state, audio_data, &session_id).await
        .map_err(|_| StatusCode::INTERNAL_SERVER_ERROR)?;

    // Update metrics
    update_metrics(&state, &latency).await;

    Ok(Json(VoiceChatResponse {
        transcript,
        response: llm_response,
        audio_url,
        session_id,
        latency,
    }))
}

async fn process_voice_pipeline(
    state: &AppState,
    audio_data: Vec<u8>,
    session_id: &str,
) -> anyhow::Result<(String, String, String, LatencyMetrics)> {
    let total_start = Instant::now();
    
    // Step 1: Speech-to-Text (STT)
    let stt_start = Instant::now();
    let transcript = state.deepgram.transcribe(audio_data).await?;
    let stt_duration = stt_start.elapsed();
    
    if transcript.trim().is_empty() {
        return Err(anyhow::anyhow!("Empty transcript"));
    }
    
    // Step 2: Get conversation history and call LLM (parallel with TTS preparation)
    let llm_start = Instant::now();
    let conversation_history = get_conversation_history(state, session_id).await;
    let llm_response = state.openai.chat_completion(&transcript, &conversation_history).await?;
    let llm_duration = llm_start.elapsed();
    
    // Step 3: Text-to-Speech (TTS) - start immediately after LLM
    let tts_start = Instant::now();
    let audio_url = state.tts.synthesize(&llm_response).await?;
    let tts_duration = tts_start.elapsed();
    
    // Update conversation history
    update_conversation_history(state, session_id, &transcript, &llm_response).await;
    
    let total_duration = total_start.elapsed();
    
    let latency = LatencyMetrics {
        stt_ms: stt_duration.as_millis() as u64,
        llm_ms: llm_duration.as_millis() as u64,
        tts_ms: tts_duration.as_millis() as u64,
        total_ms: total_duration.as_millis() as u64,
    };
    
    info!("🎯 Pipeline completed: STT={}ms, LLM={}ms, TTS={}ms, Total={}ms", 
          latency.stt_ms, latency.llm_ms, latency.tts_ms, latency.total_ms);
    
    Ok((transcript, llm_response, audio_url, latency))
}

async fn get_conversation_history(state: &AppState, session_id: &str) -> Vec<Message> {
    state.conversations.read().await
        .get(session_id)
        .cloned()
        .unwrap_or_default()
}

async fn update_conversation_history(
    state: &AppState, 
    session_id: &str, 
    user_message: &str, 
    ai_response: &str
) {
    let mut conversations = state.conversations.write().await;
    let history = conversations.entry(session_id.to_string()).or_insert_with(Vec::new);
    
    let timestamp = std::time::SystemTime::now()
        .duration_since(std::time::UNIX_EPOCH)
        .unwrap()
        .as_secs();
    
    history.push(Message {
        role: "user".to_string(),
        content: user_message.to_string(),
        timestamp,
    });
    
    history.push(Message {
        role: "assistant".to_string(),
        content: ai_response.to_string(),
        timestamp,
    });
    
    // Keep only last 20 messages to prevent memory bloat
    if history.len() > 20 {
        history.drain(0..history.len() - 20);
    }
}

async fn update_metrics(state: &AppState, latency: &LatencyMetrics) {
    let mut metrics = state.metrics.write().await;
    metrics.total_requests += 1;
    
    let n = metrics.total_requests as f64;
    metrics.avg_stt_latency = (metrics.avg_stt_latency * (n - 1.0) + latency.stt_ms as f64) / n;
    metrics.avg_llm_latency = (metrics.avg_llm_latency * (n - 1.0) + latency.llm_ms as f64) / n;
    metrics.avg_tts_latency = (metrics.avg_tts_latency * (n - 1.0) + latency.tts_ms as f64) / n;
    metrics.avg_total_latency = (metrics.avg_total_latency * (n - 1.0) + latency.total_ms as f64) / n;
}

async fn voice_chat_stream_handler(
    State(state): State<AppState>,
    mut multipart: Multipart,
) -> Result<impl IntoResponse, StatusCode> {
    // Streaming implementation for real-time processing
    Ok(StatusCode::NOT_IMPLEMENTED)
}

async fn transcribe_handler(
    State(state): State<AppState>,
    mut multipart: Multipart,
) -> Result<Json<serde_json::Value>, StatusCode> {
    let mut audio_data = Vec::new();

    while let Some(field) = multipart.next_field().await.map_err(|_| StatusCode::BAD_REQUEST)? {
        if field.name() == Some("audio") {
            audio_data = field.bytes().await.map_err(|_| StatusCode::BAD_REQUEST)?.to_vec();
            break;
        }
    }

    if audio_data.is_empty() {
        return Err(StatusCode::BAD_REQUEST);
    }

    let transcript = state.deepgram.transcribe(audio_data).await
        .map_err(|_| StatusCode::INTERNAL_SERVER_ERROR)?;

    Ok(Json(serde_json::json!({
        "transcript": transcript,
        "status": "success"
    })))
}

async fn chat_handler(
    State(state): State<AppState>,
    Json(payload): Json<serde_json::Value>,
) -> Result<Json<serde_json::Value>, StatusCode> {
    let message = payload.get("message")
        .and_then(|m| m.as_str())
        .ok_or(StatusCode::BAD_REQUEST)?;

    let response = state.openai.chat_completion(message, &[]).await
        .map_err(|_| StatusCode::INTERNAL_SERVER_ERROR)?;

    Ok(Json(serde_json::json!({
        "response": response,
        "status": "success"
    })))
}

async fn speak_handler(
    State(state): State<AppState>,
    Json(payload): Json<serde_json::Value>,
) -> Result<impl IntoResponse, StatusCode> {
    let text = payload.get("text")
        .and_then(|t| t.as_str())
        .ok_or(StatusCode::BAD_REQUEST)?;

    let audio_path = state.tts.synthesize(text).await
        .map_err(|_| StatusCode::INTERNAL_SERVER_ERROR)?;

    // Read audio file and return as response
    let audio_data = tokio::fs::read(&audio_path).await
        .map_err(|_| StatusCode::INTERNAL_SERVER_ERROR)?;

    // Clean up temp file
    let _ = tokio::fs::remove_file(&audio_path).await;

    Ok((
        [("Content-Type", "audio/wav")],
        audio_data
    ))
}

async fn metrics_handler(State(state): State<AppState>) -> Json<Metrics> {
    let metrics = state.metrics.read().await.clone();
    Json(metrics)
}

async fn websocket_handler(ws: WebSocketUpgrade) -> Response {
    ws.on_upgrade(websocket::handle_socket)
}
