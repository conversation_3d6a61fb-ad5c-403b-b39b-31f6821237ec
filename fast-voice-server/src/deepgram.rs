use anyhow::Result;
use reqwest::Client;
use serde::{Deserialize, Serialize};
use std::time::Instant;
use tracing::{info, error};

#[derive(Clone)]
pub struct DeepgramClient {
    client: Client,
    api_key: String,
    base_url: String,
}

#[derive(Debug, Serialize, Deserialize)]
struct DeepgramRequest {
    #[serde(skip_serializing_if = "Option::is_none")]
    model: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    language: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    smart_format: Option<bool>,
    #[serde(skip_serializing_if = "Option::is_none")]
    punctuate: Option<bool>,
    #[serde(skip_serializing_if = "Option::is_none")]
    diarize: Option<bool>,
    #[serde(skip_serializing_if = "Option::is_none")]
    alternatives: Option<u32>,
}

#[derive(Debug, Deserialize)]
struct DeepgramResponse {
    results: DeepgramResults,
}

#[derive(Debug, Deserialize)]
struct DeepgramResults {
    channels: Vec<DeepgramChannel>,
}

#[derive(Debug, Deserialize)]
struct DeepgramChannel {
    alternatives: Vec<DeepgramAlternative>,
}

#[derive(Debug, Deserialize)]
struct DeepgramAlternative {
    transcript: String,
    confidence: f64,
}

impl DeepgramClient {
    pub fn new(api_key: String) -> Self {
        let client = Client::builder()
            .timeout(std::time::Duration::from_secs(30))
            .build()
            .expect("Failed to create HTTP client");

        Self {
            client,
            api_key,
            base_url: "https://api.deepgram.com/v1/listen".to_string(),
        }
    }

    pub async fn transcribe(&self, audio_data: Vec<u8>) -> Result<String> {
        let start = Instant::now();
        
        // Try multiple language configurations for best results
        let configs = vec![
            DeepgramRequest {
                model: Some("nova-2".to_string()),
                language: Some("sk".to_string()),
                smart_format: Some(true),
                punctuate: Some(true),
                diarize: Some(false),
                alternatives: Some(3),
            },
            DeepgramRequest {
                model: Some("nova-2".to_string()),
                language: Some("cs".to_string()),
                smart_format: Some(true),
                punctuate: Some(true),
                diarize: Some(false),
                alternatives: Some(2),
            },
            DeepgramRequest {
                model: Some("nova-2".to_string()),
                language: Some("en".to_string()),
                smart_format: Some(true),
                punctuate: Some(true),
                diarize: Some(false),
                alternatives: Some(1),
            },
        ];

        for (i, config) in configs.iter().enumerate() {
            match self.transcribe_with_config(audio_data.clone(), config).await {
                Ok(transcript) if !transcript.trim().is_empty() => {
                    let duration = start.elapsed();
                    info!("🎤 Deepgram STT success (config {}): {}ms - '{}'", 
                          i + 1, duration.as_millis(), transcript);
                    return Ok(transcript);
                }
                Ok(_) => {
                    info!("🎤 Deepgram config {} returned empty transcript", i + 1);
                }
                Err(e) => {
                    error!("🎤 Deepgram config {} failed: {}", i + 1, e);
                }
            }
        }

        Err(anyhow::anyhow!("All Deepgram configurations failed"))
    }

    async fn transcribe_with_config(
        &self,
        audio_data: Vec<u8>,
        config: &DeepgramRequest,
    ) -> Result<String> {
        let mut url = reqwest::Url::parse(&self.base_url)?;
        
        // Add query parameters
        if let Some(model) = &config.model {
            url.query_pairs_mut().append_pair("model", model);
        }
        if let Some(language) = &config.language {
            url.query_pairs_mut().append_pair("language", language);
        }
        if let Some(smart_format) = config.smart_format {
            url.query_pairs_mut().append_pair("smart_format", &smart_format.to_string());
        }
        if let Some(punctuate) = config.punctuate {
            url.query_pairs_mut().append_pair("punctuate", &punctuate.to_string());
        }
        if let Some(diarize) = config.diarize {
            url.query_pairs_mut().append_pair("diarize", &diarize.to_string());
        }
        if let Some(alternatives) = config.alternatives {
            url.query_pairs_mut().append_pair("alternatives", &alternatives.to_string());
        }

        let response = self
            .client
            .post(url)
            .header("Authorization", format!("Token {}", self.api_key))
            .header("Content-Type", "audio/wav")
            .body(audio_data)
            .send()
            .await?;

        if !response.status().is_success() {
            let status = response.status();
            let error_text = response.text().await.unwrap_or_default();
            return Err(anyhow::anyhow!("Deepgram API error {}: {}", status, error_text));
        }

        let deepgram_response: DeepgramResponse = response.json().await?;
        
        // Extract the best transcript
        let transcript = deepgram_response
            .results
            .channels
            .first()
            .and_then(|channel| channel.alternatives.first())
            .map(|alt| alt.transcript.clone())
            .unwrap_or_default();

        Ok(transcript)
    }

    pub async fn transcribe_streaming(&self, audio_stream: tokio::sync::mpsc::Receiver<Vec<u8>>) -> Result<tokio::sync::mpsc::Receiver<String>> {
        // Implementation for streaming STT
        // This would use Deepgram's streaming API for real-time transcription
        todo!("Implement streaming transcription")
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_deepgram_client_creation() {
        let client = DeepgramClient::new("test_key".to_string());
        assert_eq!(client.api_key, "test_key");
    }
}
