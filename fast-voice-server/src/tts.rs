use anyhow::Result;
use std::{
    collections::HashMap,
    path::PathBuf,
    process::Stdio,
    sync::Arc,
    time::Instant,
};
use tokio::{
    fs,
    io::{AsyncReadExt, AsyncWriteExt},
    process::Command,
    sync::RwLock,
};
use tracing::{info, error, warn};
use uuid::Uuid;

#[derive(Clone)]
pub struct TTSEngine {
    piper_path: PathBuf,
    model_path: PathBuf,
    cache: Arc<RwLock<HashMap<String, String>>>,
    temp_dir: PathBuf,
}

impl TTSEngine {
    pub async fn new() -> Result<Self> {
        let piper_path = PathBuf::from("/home/<USER>/piper_env/bin/piper");
        let model_path = PathBuf::from("/home/<USER>/models/sk_SK-lili-medium.onnx");
        let temp_dir = PathBuf::from("/tmp/tts_cache");
        
        // Create temp directory if it doesn't exist
        if !temp_dir.exists() {
            fs::create_dir_all(&temp_dir).await?;
        }

        // Verify Piper installation
        if !piper_path.exists() {
            return Err(anyhow::anyhow!("Piper TTS not found at {:?}", piper_path));
        }

        if !model_path.exists() {
            return Err(anyhow::anyhow!("TTS model not found at {:?}", model_path));
        }

        info!("🔊 TTS Engine initialized with Piper at {:?}", piper_path);
        info!("🎵 Using Slovak model: {:?}", model_path);

        Ok(Self {
            piper_path,
            model_path,
            cache: Arc::new(RwLock::new(HashMap::new())),
            temp_dir,
        })
    }

    pub async fn synthesize(&self, text: &str) -> Result<String> {
        let start = Instant::now();
        
        // Check cache first
        let cache_key = self.generate_cache_key(text);
        {
            let cache = self.cache.read().await;
            if let Some(cached_path) = cache.get(&cache_key) {
                if PathBuf::from(cached_path).exists() {
                    info!("🎵 TTS cache hit: {}ms", start.elapsed().as_millis());
                    return Ok(cached_path.clone());
                }
            }
        }

        // Generate new audio
        let audio_path = self.generate_audio(text).await?;
        
        // Cache the result
        {
            let mut cache = self.cache.write().await;
            cache.insert(cache_key, audio_path.clone());
            
            // Limit cache size to prevent memory bloat
            if cache.len() > 100 {
                let keys_to_remove: Vec<String> = cache.keys().take(20).cloned().collect();
                for key in keys_to_remove {
                    if let Some(old_path) = cache.remove(&key) {
                        let _ = fs::remove_file(&old_path).await;
                    }
                }
            }
        }

        let duration = start.elapsed();
        info!("🔊 TTS synthesis: {}ms - '{}'", duration.as_millis(), 
              text.chars().take(50).collect::<String>());

        Ok(audio_path)
    }

    async fn generate_audio(&self, text: &str) -> Result<String> {
        let audio_id = Uuid::new_v4();
        let output_path = self.temp_dir.join(format!("{}.wav", audio_id));
        
        // Use Piper TTS with optimized settings for speed
        let mut cmd = Command::new(&self.piper_path)
            .arg("--model")
            .arg(&self.model_path)
            .arg("--output_file")
            .arg(&output_path)
            .arg("--length_scale")
            .arg("1.0") // Normal speed
            .arg("--noise_scale")
            .arg("0.667") // Slight variation for naturalness
            .arg("--noise_w")
            .arg("0.8") // Phoneme duration variation
            .stdin(Stdio::piped())
            .stdout(Stdio::piped())
            .stderr(Stdio::piped())
            .spawn()?;

        // Write text to stdin
        if let Some(stdin) = cmd.stdin.as_mut() {
            stdin.write_all(text.as_bytes()).await?;
            stdin.flush().await?;
        }

        // Wait for completion
        let output = cmd.wait_with_output().await?;

        if !output.status.success() {
            let stderr = String::from_utf8_lossy(&output.stderr);
            error!("Piper TTS failed: {}", stderr);
            return Err(anyhow::anyhow!("TTS generation failed: {}", stderr));
        }

        // Verify output file exists and has content
        if !output_path.exists() {
            return Err(anyhow::anyhow!("TTS output file not created"));
        }

        let metadata = fs::metadata(&output_path).await?;
        if metadata.len() == 0 {
            return Err(anyhow::anyhow!("TTS output file is empty"));
        }

        Ok(output_path.to_string_lossy().to_string())
    }

    fn generate_cache_key(&self, text: &str) -> String {
        use std::collections::hash_map::DefaultHasher;
        use std::hash::{Hash, Hasher};
        
        let mut hasher = DefaultHasher::new();
        text.hash(&mut hasher);
        format!("tts_{:x}", hasher.finish())
    }

    pub async fn synthesize_streaming(&self, text_stream: tokio::sync::mpsc::Receiver<String>) -> Result<tokio::sync::mpsc::Receiver<Vec<u8>>> {
        let (tx, rx) = tokio::sync::mpsc::channel(100);
        
        let engine = self.clone();
        tokio::spawn(async move {
            // Implementation for streaming TTS
            // This would process text chunks as they arrive and stream audio back
            // For now, we'll collect all text and synthesize at once
            
            // TODO: Implement true streaming TTS
            warn!("Streaming TTS not yet implemented, falling back to batch processing");
        });

        Ok(rx)
    }

    pub async fn preload_common_phrases(&self) -> Result<()> {
        let common_phrases = vec![
            "Ahoj, ako sa máš?",
            "Ďakujem za otázku.",
            "Prepáčte, nerozumiem.",
            "Môžete to zopakovať?",
            "To je zaujímavé.",
            "Súhlasím s vami.",
            "Nie som si istý.",
            "Skúsim vám pomôcť.",
        ];

        info!("🔄 Preloading {} common TTS phrases...", common_phrases.len());
        
        for phrase in common_phrases {
            if let Err(e) = self.synthesize(phrase).await {
                warn!("Failed to preload phrase '{}': {}", phrase, e);
            }
        }

        info!("✅ TTS phrase preloading completed");
        Ok(())
    }

    pub async fn cleanup_old_files(&self) -> Result<()> {
        let mut entries = fs::read_dir(&self.temp_dir).await?;
        let now = std::time::SystemTime::now();
        let mut cleaned = 0;

        while let Some(entry) = entries.next_entry().await? {
            let path = entry.path();
            if let Ok(metadata) = entry.metadata().await {
                if let Ok(created) = metadata.created() {
                    if let Ok(age) = now.duration_since(created) {
                        // Remove files older than 1 hour
                        if age > std::time::Duration::from_secs(3600) {
                            if let Err(e) = fs::remove_file(&path).await {
                                warn!("Failed to remove old TTS file {:?}: {}", path, e);
                            } else {
                                cleaned += 1;
                            }
                        }
                    }
                }
            }
        }

        if cleaned > 0 {
            info!("🧹 Cleaned up {} old TTS files", cleaned);
        }

        Ok(())
    }

    pub fn get_cache_stats(&self) -> (usize, usize) {
        // Returns (cache_size, estimated_memory_usage)
        // This is a rough estimate
        (0, 0) // TODO: Implement proper cache statistics
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_cache_key_generation() {
        let engine = TTSEngine {
            piper_path: PathBuf::new(),
            model_path: PathBuf::new(),
            cache: Arc::new(RwLock::new(HashMap::new())),
            temp_dir: PathBuf::new(),
        };

        let key1 = engine.generate_cache_key("Hello world");
        let key2 = engine.generate_cache_key("Hello world");
        let key3 = engine.generate_cache_key("Different text");

        assert_eq!(key1, key2);
        assert_ne!(key1, key3);
    }
}
