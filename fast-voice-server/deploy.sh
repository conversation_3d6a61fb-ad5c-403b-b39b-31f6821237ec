#!/bin/bash

# Fast Voice Server Deployment Script
# Optimized for Oracle Cloud Ubuntu instance

set -e

echo "🚀 Starting Fast Voice Server deployment..."

# Configuration
REMOTE_HOST="ubuntu@*************"
SSH_KEY="ssh-key-2025-07-16 (3).key"
REMOTE_DIR="/home/<USER>/fast-voice-server"
SERVICE_NAME="fast-voice-server"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

success() {
    echo -e "${GREEN}✅ $1${NC}"
}

warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

error() {
    echo -e "${RED}❌ $1${NC}"
    exit 1
}

# Check if SSH key exists
if [ ! -f "$SSH_KEY" ]; then
    error "SSH key not found: $SSH_KEY"
fi

# Build the Rust application
log "Building Rust application..."
if ! cargo build --release; then
    error "Failed to build Rust application"
fi
success "Rust application built successfully"

# Create deployment package
log "Creating deployment package..."
DEPLOY_DIR="deploy_$(date +%Y%m%d_%H%M%S)"
mkdir -p "$DEPLOY_DIR"

# Copy binary and configuration files
cp target/release/fast-voice-server "$DEPLOY_DIR/"
cp -r src "$DEPLOY_DIR/" # For reference
cp Cargo.toml "$DEPLOY_DIR/"

# Create systemd service file
cat > "$DEPLOY_DIR/fast-voice-server.service" << EOF
[Unit]
Description=Fast Voice Server - Optimized Rust Voice Chat
After=network.target

[Service]
Type=simple
User=ubuntu
WorkingDirectory=$REMOTE_DIR
ExecStart=$REMOTE_DIR/fast-voice-server
Restart=always
RestartSec=10
Environment=RUST_LOG=info
Environment=RUST_BACKTRACE=1

# Performance optimizations
LimitNOFILE=65536
LimitNPROC=32768

# Security
NoNewPrivileges=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=$REMOTE_DIR /tmp

[Install]
WantedBy=multi-user.target
EOF

# Create installation script
cat > "$DEPLOY_DIR/install.sh" << 'EOF'
#!/bin/bash
set -e

echo "🔧 Installing Fast Voice Server..."

# Stop existing service if running
sudo systemctl stop fast-voice-server 2>/dev/null || true

# Install binary
sudo cp fast-voice-server /usr/local/bin/
sudo chmod +x /usr/local/bin/fast-voice-server

# Install systemd service
sudo cp fast-voice-server.service /etc/systemd/system/
sudo systemctl daemon-reload
sudo systemctl enable fast-voice-server

# Create log directory
sudo mkdir -p /var/log/fast-voice-server
sudo chown ubuntu:ubuntu /var/log/fast-voice-server

# Start service
sudo systemctl start fast-voice-server

echo "✅ Fast Voice Server installed and started"
echo "📊 Check status: sudo systemctl status fast-voice-server"
echo "📋 View logs: sudo journalctl -u fast-voice-server -f"
EOF

chmod +x "$DEPLOY_DIR/install.sh"

# Create archive
log "Creating deployment archive..."
tar -czf "${DEPLOY_DIR}.tar.gz" "$DEPLOY_DIR"
success "Deployment package created: ${DEPLOY_DIR}.tar.gz"

# Upload to server
log "Uploading to Oracle server..."
if ! scp -i "$SSH_KEY" "${DEPLOY_DIR}.tar.gz" "$REMOTE_HOST:/tmp/"; then
    error "Failed to upload deployment package"
fi
success "Deployment package uploaded"

# Deploy on server
log "Deploying on server..."
ssh -i "$SSH_KEY" "$REMOTE_HOST" << EOF
    set -e
    cd /tmp
    
    # Extract deployment package
    tar -xzf ${DEPLOY_DIR}.tar.gz
    cd ${DEPLOY_DIR}
    
    # Run installation
    chmod +x install.sh
    ./install.sh
    
    # Cleanup
    cd ..
    rm -rf ${DEPLOY_DIR} ${DEPLOY_DIR}.tar.gz
    
    echo "🎯 Deployment completed successfully!"
    echo "🔍 Server status:"
    sudo systemctl status fast-voice-server --no-pager
EOF

if [ $? -eq 0 ]; then
    success "Deployment completed successfully!"
    
    # Test the deployment
    log "Testing deployment..."
    sleep 5
    
    if curl -s "http://*************:5000/health" > /dev/null; then
        success "Server is responding to health checks"
    else
        warning "Server health check failed - may need a moment to start"
    fi
    
    echo ""
    echo "🎉 Fast Voice Server deployment complete!"
    echo "📡 Server URL: http://*************:5000"
    echo "🔍 Health check: curl http://*************:5000/health"
    echo "📊 Metrics: curl http://*************:5000/api/metrics"
    echo "📋 View logs: ssh -i '$SSH_KEY' $REMOTE_HOST 'sudo journalctl -u fast-voice-server -f'"
    echo ""
    echo "🚀 Ready for high-performance voice chat!"
    
else
    error "Deployment failed"
fi

# Cleanup local files
rm -rf "$DEPLOY_DIR" "${DEPLOY_DIR}.tar.gz"
EOF
