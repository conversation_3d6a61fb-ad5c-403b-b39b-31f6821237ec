[package]
name = "fast-voice-server"
version = "0.1.0"
edition = "2021"

[dependencies]
tokio = { version = "1.0", features = ["full"] }
axum = "0.7"
tower = "0.4"
tower-http = { version = "0.5", features = ["cors"] }
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
reqwest = { version = "0.11", features = ["json", "multipart", "stream"] }
bytes = "1.0"
futures = "0.3"
tracing = "0.1"
tracing-subscriber = "0.3"
anyhow = "1.0"
base64 = "0.21"
uuid = { version = "1.0", features = ["v4"] }

# Audio processing
hound = "3.5"
rodio = "0.17"
cpal = "0.15"

# WebSocket support
axum-tungstenite = "0.2"
tokio-tungstenite = "0.20"

# Memory management
parking_lot = "0.12"
dashmap = "5.5"

# Performance monitoring
metrics = "0.21"
metrics-exporter-prometheus = "0.12"
