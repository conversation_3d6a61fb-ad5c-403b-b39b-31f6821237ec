<!DOCTYPE html>
<html lang="sk">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🚀 Fast Voice Server Test</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 20px;
        }

        .container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            width: 100%;
            max-width: 900px;
            padding: 30px;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
        }

        .header h1 {
            color: #333;
            margin-bottom: 10px;
        }

        .header .subtitle {
            color: #666;
            font-size: 16px;
        }

        .metrics {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 30px;
        }

        .metric {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 10px;
            text-align: center;
        }

        .metric-value {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
        }

        .metric-label {
            font-size: 12px;
            color: #666;
            margin-top: 5px;
        }

        .controls {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin-bottom: 30px;
            flex-wrap: wrap;
        }

        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 500;
            transition: all 0.3s ease;
            min-width: 120px;
        }

        button:hover {
            background: #0056b3;
            transform: translateY(-2px);
        }

        button:disabled {
            background: #6c757d;
            cursor: not-allowed;
            transform: none;
        }

        .recording {
            background: #dc3545 !important;
            animation: pulse 1s infinite;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.7; }
            100% { opacity: 1; }
        }

        .status {
            text-align: center;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            font-weight: 500;
        }

        .status.connected { background: #d4edda; color: #155724; }
        .status.disconnected { background: #f8d7da; color: #721c24; }
        .status.processing { background: #fff3cd; color: #856404; }
        .status.listening { background: #cce5ff; color: #004085; }

        .audio-visualizer {
            width: 100%;
            height: 60px;
            background: #f8f9fa;
            border-radius: 10px;
            margin: 20px 0;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
        }

        .visualizer-bar {
            width: 4px;
            background: #007bff;
            margin: 0 1px;
            border-radius: 2px;
            transition: height 0.1s ease;
        }

        .conversation {
            max-height: 300px;
            overflow-y: auto;
            border: 1px solid #dee2e6;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 20px;
        }

        .message {
            margin-bottom: 15px;
            padding: 10px;
            border-radius: 10px;
        }

        .message.user {
            background: #e3f2fd;
            margin-left: 20px;
        }

        .message.assistant {
            background: #f3e5f5;
            margin-right: 20px;
        }

        .message-time {
            font-size: 11px;
            color: #666;
            margin-bottom: 5px;
        }

        .logs {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 15px;
            max-height: 200px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            line-height: 1.4;
        }

        .log-entry {
            margin-bottom: 5px;
        }

        .log-time {
            color: #666;
        }

        .log-info { color: #007bff; }
        .log-success { color: #28a745; }
        .log-warning { color: #ffc107; }
        .log-error { color: #dc3545; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 Fast Voice Server Test</h1>
            <p class="subtitle">Ultra-Low Latency Voice Chat - Rust Edition</p>
        </div>

        <div class="metrics">
            <div class="metric">
                <div class="metric-value" id="sttLatency">-</div>
                <div class="metric-label">STT Latency (ms)</div>
            </div>
            <div class="metric">
                <div class="metric-value" id="llmLatency">-</div>
                <div class="metric-label">LLM Latency (ms)</div>
            </div>
            <div class="metric">
                <div class="metric-value" id="ttsLatency">-</div>
                <div class="metric-label">TTS Latency (ms)</div>
            </div>
            <div class="metric">
                <div class="metric-value" id="totalLatency">-</div>
                <div class="metric-label">Total Latency (ms)</div>
            </div>
        </div>

        <div id="status" class="status disconnected">
            Pripravený na pripojenie
        </div>

        <div class="controls">
            <button id="connectBtn" onclick="testConnection()">Test pripojenia</button>
            <button id="recordBtn" onclick="toggleRecording()" disabled>Spustiť nahrávanie</button>
            <button id="streamBtn" onclick="toggleStreaming()" disabled>Streaming režim</button>
            <button id="metricsBtn" onclick="loadMetrics()">Načítať metriky</button>
        </div>

        <div class="audio-visualizer" id="visualizer">
            <!-- Audio visualization bars will be added here -->
        </div>

        <div class="conversation" id="conversation">
            <div class="message assistant">
                <div class="message-time">System</div>
                <div>Vitajte v Fast Voice Server teste! Kliknite na "Test pripojenia" pre začiatok.</div>
            </div>
        </div>

        <div class="logs" id="logs">
            <div class="log-entry">
                <span class="log-time">[System]</span>
                <span class="log-info">Fast Voice Server Test načítaný</span>
            </div>
        </div>
    </div>

    <script>
        // Global variables
        let isConnected = false;
        let isRecording = false;
        let isStreaming = false;
        let mediaRecorder = null;
        let audioContext = null;
        let analyser = null;
        let websocket = null;
        let sessionId = null;

        // Server configuration
        const SERVER_URL = 'http://localhost:8888'; // Through SSH tunnel
        const WS_URL = 'ws://localhost:8888/ws';

        // Initialize visualizer
        function initVisualizer() {
            const visualizer = document.getElementById('visualizer');
            for (let i = 0; i < 50; i++) {
                const bar = document.createElement('div');
                bar.className = 'visualizer-bar';
                bar.style.height = '2px';
                visualizer.appendChild(bar);
            }
        }

        // Logging function
        function log(message, type = 'info') {
            const logs = document.getElementById('logs');
            const time = new Date().toLocaleTimeString();
            const entry = document.createElement('div');
            entry.className = 'log-entry';
            entry.innerHTML = `<span class="log-time">[${time}]</span> <span class="log-${type}">${message}</span>`;
            logs.appendChild(entry);
            logs.scrollTop = logs.scrollHeight;
            console.log(`[${type.toUpperCase()}] ${message}`);
        }

        // Update status
        function updateStatus(message, className) {
            const status = document.getElementById('status');
            status.textContent = message;
            status.className = `status ${className}`;
        }

        // Test connection to Rust server
        async function testConnection() {
            try {
                log('🔗 Testujem pripojenie na Fast Voice Server...', 'info');
                updateStatus('Testujem pripojenie...', 'processing');

                const response = await fetch(`${SERVER_URL}/health`, {
                    method: 'GET',
                    headers: { 'Accept': 'text/plain' }
                });

                if (response.ok) {
                    const text = await response.text();
                    log(`✅ Server pripojený: ${text}`, 'success');
                    updateStatus('✅ Fast Voice Server pripojený', 'connected');
                    isConnected = true;
                    
                    // Enable controls
                    document.getElementById('recordBtn').disabled = false;
                    document.getElementById('streamBtn').disabled = false;
                    
                    // Load initial metrics
                    await loadMetrics();
                } else {
                    throw new Error(`Server error: ${response.status}`);
                }
            } catch (error) {
                log(`❌ Chyba pripojenia: ${error.message}`, 'error');
                updateStatus('❌ Chyba pripojenia', 'disconnected');
                isConnected = false;
            }
        }

        // Load server metrics
        async function loadMetrics() {
            if (!isConnected) return;

            try {
                const response = await fetch(`${SERVER_URL}/api/metrics`);
                if (response.ok) {
                    const metrics = await response.json();
                    
                    document.getElementById('sttLatency').textContent = 
                        Math.round(metrics.avg_stt_latency || 0);
                    document.getElementById('llmLatency').textContent = 
                        Math.round(metrics.avg_llm_latency || 0);
                    document.getElementById('ttsLatency').textContent = 
                        Math.round(metrics.avg_tts_latency || 0);
                    document.getElementById('totalLatency').textContent = 
                        Math.round(metrics.avg_total_latency || 0);
                    
                    log(`📊 Metriky načítané: ${metrics.total_requests} požiadaviek`, 'info');
                }
            } catch (error) {
                log(`❌ Chyba pri načítaní metrík: ${error.message}`, 'error');
            }
        }

        // Toggle recording
        async function toggleRecording() {
            if (!isConnected) {
                log('❌ Nie je pripojenie na server', 'error');
                return;
            }

            if (isRecording) {
                stopRecording();
            } else {
                await startRecording();
            }
        }

        // Start recording
        async function startRecording() {
            try {
                log('🎤 Spúšťam nahrávanie...', 'info');
                updateStatus('Žiadam o prístup k mikrofónu...', 'processing');

                const stream = await navigator.mediaDevices.getUserMedia({
                    audio: {
                        echoCancellation: true,
                        noiseSuppression: true,
                        autoGainControl: true,
                        sampleRate: 16000
                    }
                });

                // Setup audio context for visualization
                audioContext = new (window.AudioContext || window.webkitAudioContext)();
                analyser = audioContext.createAnalyser();
                const source = audioContext.createMediaStreamSource(stream);
                source.connect(analyser);
                analyser.fftSize = 256;

                // Setup media recorder
                mediaRecorder = new MediaRecorder(stream, {
                    mimeType: 'audio/webm;codecs=opus'
                });

                const audioChunks = [];
                mediaRecorder.ondataavailable = (event) => {
                    if (event.data.size > 0) {
                        audioChunks.push(event.data);
                    }
                };

                mediaRecorder.onstop = async () => {
                    const audioBlob = new Blob(audioChunks, { type: 'audio/webm' });
                    await processAudio(audioBlob);
                };

                mediaRecorder.start();
                isRecording = true;

                // Update UI
                const recordBtn = document.getElementById('recordBtn');
                recordBtn.textContent = 'Zastaviť nahrávanie';
                recordBtn.classList.add('recording');
                updateStatus('🎤 Nahrávam... (hovorte po slovensky)', 'listening');

                // Start visualization
                visualizeAudio();

                log('✅ Nahrávanie spustené', 'success');

            } catch (error) {
                log(`❌ Chyba pri spustení nahrávania: ${error.message}`, 'error');
                updateStatus('❌ Chyba pri prístupe k mikrofónu', 'disconnected');
            }
        }

        // Stop recording
        function stopRecording() {
            if (mediaRecorder && mediaRecorder.state !== 'inactive') {
                mediaRecorder.stop();
            }

            if (audioContext) {
                audioContext.close();
                audioContext = null;
            }

            isRecording = false;

            // Update UI
            const recordBtn = document.getElementById('recordBtn');
            recordBtn.textContent = 'Spustiť nahrávanie';
            recordBtn.classList.remove('recording');
            updateStatus('⏹️ Nahrávanie zastavené', 'processing');

            log('⏹️ Nahrávanie zastavené', 'info');
        }

        // Process audio through Fast Voice Server
        async function processAudio(audioBlob) {
            try {
                log(`🎵 Spracovávam audio (${audioBlob.size} bajtov)...`, 'info');
                updateStatus('🚀 Spracovávam cez Fast Voice Server...', 'processing');

                const formData = new FormData();
                formData.append('audio', audioBlob, 'recording.webm');

                const startTime = Date.now();
                const response = await fetch(`${SERVER_URL}/api/voice-chat`, {
                    method: 'POST',
                    body: formData
                });

                if (response.ok) {
                    const result = await response.json();
                    const totalTime = Date.now() - startTime;

                    // Update metrics display
                    document.getElementById('sttLatency').textContent = result.latency.stt_ms;
                    document.getElementById('llmLatency').textContent = result.latency.llm_ms;
                    document.getElementById('ttsLatency').textContent = result.latency.tts_ms;
                    document.getElementById('totalLatency').textContent = result.latency.total_ms;

                    // Add to conversation
                    addMessage('user', result.transcript);
                    addMessage('assistant', result.response);

                    // Play audio response if available
                    if (result.audio_url) {
                        playAudioResponse(result.audio_url);
                    }

                    log(`✅ Spracovanie dokončené za ${totalTime}ms`, 'success');
                    log(`📝 Transkript: "${result.transcript}"`, 'info');
                    log(`🤖 Odpoveď: "${result.response}"`, 'info');

                    updateStatus('✅ Pripravený na ďalšie nahrávanie', 'connected');

                } else {
                    const error = await response.text();
                    throw new Error(`Server error: ${response.status} - ${error}`);
                }

            } catch (error) {
                log(`❌ Chyba pri spracovaní: ${error.message}`, 'error');
                updateStatus('❌ Chyba pri spracovaní', 'disconnected');
            }
        }

        // Add message to conversation
        function addMessage(role, content) {
            const conversation = document.getElementById('conversation');
            const message = document.createElement('div');
            message.className = `message ${role}`;
            
            const time = new Date().toLocaleTimeString();
            message.innerHTML = `
                <div class="message-time">${time} - ${role === 'user' ? 'Vy' : 'AI'}</div>
                <div>${content}</div>
            `;
            
            conversation.appendChild(message);
            conversation.scrollTop = conversation.scrollHeight;
        }

        // Play audio response
        function playAudioResponse(audioUrl) {
            const audio = new Audio(audioUrl);
            audio.play().catch(error => {
                log(`❌ Chyba pri prehrávaní: ${error.message}`, 'error');
            });
        }

        // Visualize audio
        function visualizeAudio() {
            if (!analyser || !isRecording) return;

            const bufferLength = analyser.frequencyBinCount;
            const dataArray = new Uint8Array(bufferLength);
            analyser.getByteFrequencyData(dataArray);

            const bars = document.querySelectorAll('.visualizer-bar');
            const step = Math.floor(bufferLength / bars.length);

            for (let i = 0; i < bars.length; i++) {
                const value = dataArray[i * step];
                const height = Math.max(2, (value / 255) * 50);
                bars[i].style.height = height + 'px';
            }

            if (isRecording) {
                requestAnimationFrame(visualizeAudio);
            }
        }

        // Toggle streaming mode
        function toggleStreaming() {
            if (isStreaming) {
                stopStreaming();
            } else {
                startStreaming();
            }
        }

        // Start streaming mode
        function startStreaming() {
            log('🌊 Streaming režim zatiaľ nie je implementovaný', 'warning');
            // TODO: Implement WebSocket streaming
        }

        // Stop streaming mode
        function stopStreaming() {
            log('⏹️ Streaming zastavený', 'info');
        }

        // Initialize on page load
        window.addEventListener('load', () => {
            log('🚀 Fast Voice Server Test inicializovaný', 'success');
            initVisualizer();
            
            // Auto-test connection after 1 second
            setTimeout(testConnection, 1000);
        });

        // Cleanup on page unload
        window.addEventListener('beforeunload', () => {
            if (isRecording) {
                stopRecording();
            }
            if (websocket) {
                websocket.close();
            }
        });
    </script>
</body>
</html>
